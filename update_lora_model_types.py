#!/usr/bin/env python3
"""
更新现有LoRA模型配置，添加model_type字段
"""

import json
import sys
from pathlib import Path

def detect_model_type(name: str, filename: str, file_path: str) -> str:
    """检测模型类型（flux/sdxl/sd15）"""
    name_lower = name.lower()
    filename_lower = filename.lower()
    path_lower = file_path.lower()
    
    # 检查路径中的明确指示
    if '/sdxl/' in path_lower or '\\sdxl\\' in path_lower:
        return 'sdxl'
    if '/sd15/' in path_lower or '\\sd15\\' in path_lower or '/sd1.5/' in path_lower:
        return 'sd15'
    
    # 检查文件名中的关键词
    flux_keywords = ['flux', 'flux1', 'flux.1']
    sdxl_keywords = ['sdxl', 'xl', 'lightning']
    sd15_keywords = ['sd15', 'sd1.5', 'sd_1.5']
    
    # 优先检查明确的关键词
    for keyword in flux_keywords:
        if keyword in name_lower or keyword in filename_lower:
            return 'flux'
    
    for keyword in sdxl_keywords:
        if keyword in name_lower or keyword in filename_lower:
            return 'sdxl'
            
    for keyword in sd15_keywords:
        if keyword in name_lower or keyword in filename_lower:
            return 'sd15'
    
    # 默认返回flux（当前主要使用的模型类型）
    return 'flux'

def clean_trigger_words(trigger_words: list) -> list:
    """清理触发词，移除文件扩展名相关的词"""
    if not trigger_words:
        return []
    
    # 需要移除的扩展名相关词汇
    extension_words = {'safetensors', 'ckpt', 'pt', 'bin', 'pth', 'step', 'lightning'}
    
    cleaned_words = []
    for word in trigger_words:
        if word.lower() not in extension_words:
            cleaned_words.append(word)
    
    return cleaned_words

def update_lora_models_config():
    """更新LoRA模型配置文件"""
    config_path = Path("config/lora_models.json")
    
    if not config_path.exists():
        print(f"❌ 配置文件不存在: {config_path}")
        return False
    
    try:
        # 读取现有配置
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print(f"📖 读取配置文件: {config_path}")
        print(f"📊 现有模型数量: {len(config.get('models', []))}")
        
        updated_count = 0
        cleaned_count = 0
        
        # 更新每个模型
        for model in config.get('models', []):
            # 添加model_type字段（如果不存在）
            if 'model_type' not in model:
                model_type = detect_model_type(
                    model.get('name', ''),
                    model.get('filename', ''),
                    model.get('file_path', '')
                )
                model['model_type'] = model_type
                updated_count += 1
                print(f"✅ {model['name']}: 添加model_type = {model_type}")
            
            # 清理触发词
            original_trigger_words = model.get('trigger_words', [])
            cleaned_trigger_words = clean_trigger_words(original_trigger_words)
            
            if len(cleaned_trigger_words) != len(original_trigger_words):
                model['trigger_words'] = cleaned_trigger_words
                cleaned_count += 1
                removed_words = set(original_trigger_words) - set(cleaned_trigger_words)
                print(f"🧹 {model['name']}: 移除触发词 {removed_words}")
        
        # 保存更新后的配置
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        print(f"\n✅ 配置文件更新完成!")
        print(f"📊 添加model_type字段: {updated_count} 个模型")
        print(f"🧹 清理触发词: {cleaned_count} 个模型")
        
        # 显示模型类型统计
        type_counts = {}
        for model in config.get('models', []):
            model_type = model.get('model_type', 'unknown')
            type_counts[model_type] = type_counts.get(model_type, 0) + 1
        
        print(f"\n📈 模型类型统计:")
        for model_type, count in sorted(type_counts.items()):
            print(f"   {model_type}: {count} 个")
        
        return True
        
    except Exception as e:
        print(f"❌ 更新配置文件失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 开始更新LoRA模型配置...")
    print(f"📁 当前工作目录: {Path.cwd()}")

    try:
        if update_lora_models_config():
            print("\n🎉 更新完成！")
            print("💡 建议重启LangBot以加载新配置")
        else:
            print("\n❌ 更新失败！")
            sys.exit(1)
    except Exception as e:
        print(f"❌ 脚本执行失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
