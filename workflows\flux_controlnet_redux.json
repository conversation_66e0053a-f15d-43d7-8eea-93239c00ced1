{"1": {"inputs": {"text": "", "clip": ["165", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "2": {"inputs": {"images": ["152", 0]}, "class_type": "PreviewImage", "_meta": {"title": "Preview Image"}}, "3": {"inputs": {"clip_name": "siglip2_so400m_patch16_512.safetensors"}, "class_type": "CLIPVisionLoader", "_meta": {"title": "Load CLIP Vision"}}, "4": {"inputs": {"type": "depth", "control_net": ["153", 0]}, "class_type": "SetUnionControlNetType", "_meta": {"title": "SetUnionControlNetType"}}, "12": {"inputs": {"crop": "none", "clip_vision": ["3", 0], "image": ["244", 0]}, "class_type": "CLIPVisionEncode", "_meta": {"title": "CLIP Vision Encode"}}, "16": {"inputs": {"style_weight": 1.2000000000000002, "color_weight": 1.2000000000000002, "content_weight": 1.2000000000000002, "structure_weight": 1, "texture_weight": 1, "similarity_threshold": 1, "enhancement_base": 1.5, "conditioning": ["19", 0], "style_model": ["114", 0], "clip_vision_output": ["12", 0]}, "class_type": "StyleModelAdvancedApply", "_meta": {"title": "Style Model Advanced Apply"}}, "19": {"inputs": {"downsampling_factor": 3, "downsampling_function": "area", "mode": "center crop (square)", "weight": 0.5000000000000001, "autocrop_margin": 0.1, "conditioning": ["210", 0], "style_model": ["114", 0], "clip_vision": ["3", 0], "image": ["244", 0]}, "class_type": "ReduxAdvanced", "_meta": {"title": "ReduxAdvanced"}}, "20": {"inputs": {"images": ["39", 0]}, "class_type": "PreviewImage", "_meta": {"title": "Preview Image"}}, "21": {"inputs": {"strength": ["193", 1], "start_percent": 0, "end_percent": ["194", 0], "positive": ["181", 0], "negative": ["104", 0], "control_net": ["4", 0], "image": ["152", 0], "vae": ["98", 0]}, "class_type": "ControlNetApplyAdvanced", "_meta": {"title": "Apply ControlNet"}}, "38": {"inputs": {"start": ["194", 0], "end": 1, "conditioning": ["200", 0]}, "class_type": "ConditioningSetTimestepRange", "_meta": {"title": "ConditioningSetTimestepRange"}}, "39": {"inputs": {"preprocessor": "AnyLineArtPreprocessor_aux", "resolution": 1472, "image": ["106", 0]}, "class_type": "AIO_Preprocessor", "_meta": {"title": "AIO Aux Preprocessor"}}, "40": {"inputs": {"type": "canny/lineart/anime_lineart/mlsd", "control_net": ["154", 0]}, "class_type": "SetUnionControlNetType", "_meta": {"title": "SetUnionControlNetType"}}, "49": {"inputs": {"strength": 0.4200000000000001, "start_percent": 0, "end_percent": 0.4000000000000001, "positive": ["21", 0], "negative": ["21", 1], "control_net": ["40", 0], "image": ["39", 0], "vae": ["98", 0]}, "class_type": "ControlNetApplyAdvanced", "_meta": {"title": "Apply ControlNet"}}, "57": {"inputs": {"value": 0.6500000000000001}, "class_type": "easy float", "_meta": {"title": "CN-1深度控制强度"}}, "58": {"inputs": {"strength": 1, "conditioning": ["103", 0]}, "class_type": "ConditioningSetAreaStrength", "_meta": {"title": "ConditioningSetAreaStrength"}}, "60": {"inputs": {"add_noise": true, "noise_seed": 67977614402230, "steps": 30, "cfg": 1, "sampler_name": "euler", "scheduler": "sgm_uniform", "start_at_step": 0, "end_at_step": 10000, "noise_mode": "GPU(=A1111)", "return_with_leftover_noise": false, "batch_seed_mode": "incremental", "variation_seed": 0, "variation_strength": 0, "variation_method": "linear", "internal_seed": 0, "model": ["105", 0], "positive": ["58", 0], "negative": ["104", 0], "latent_image": ["107", 0]}, "class_type": "KSamplerAdvanced //Inspire", "_meta": {"title": "KSamplerAdvanced (inspire)"}}, "65": {"inputs": {"images": ["185", 0]}, "class_type": "PreviewImage", "_meta": {"title": "Preview Image"}}, "66": {"inputs": {"upscale_model": ["82", 0], "image": ["185", 0]}, "class_type": "ImageUpscaleWithModel", "_meta": {"title": "Upscale Image (using Model)"}}, "67": {"inputs": {"pixels": ["72", 0], "vae": ["98", 0]}, "class_type": "VAEEncode", "_meta": {"title": "VAE Encode"}}, "69": {"inputs": {"sharpen_radius": 1, "sigma": 0.4000000000000001, "alpha": 0.4000000000000001, "image": ["66", 0]}, "class_type": "ImageSharpen", "_meta": {"title": "Image Sharpen"}}, "71": {"inputs": {"coeff": 1.2000000000000002, "denoise": 0.3400000000000001}, "class_type": "GITSSchedulerFuncProvider", "_meta": {"title": "GITSScheduler Func Provider"}}, "72": {"inputs": {"upscale_method": "lanc<PERSON>s", "megapixels": 3.500000000000001, "image": ["69", 0]}, "class_type": "ImageScaleToTotalPixels", "_meta": {"title": "Scale Image to Total Pixels"}}, "73": {"inputs": {"samples": ["76", 0], "vae": ["98", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "74": {"inputs": {"images": ["236", 0]}, "class_type": "PreviewImage", "_meta": {"title": "Preview Image"}}, "76": {"inputs": {"add_noise": true, "noise_seed": 67977614402230, "steps": 25, "cfg": 1, "sampler_name": "dpmpp_2m_sde_gpu", "scheduler": "sgm_uniform", "start_at_step": 0, "end_at_step": 10000, "noise_mode": "GPU(=A1111)", "return_with_leftover_noise": false, "batch_seed_mode": "incremental", "variation_seed": 0, "variation_strength": 0, "variation_method": "linear", "internal_seed": 0, "model": ["165", 0], "positive": ["210", 0], "negative": ["104", 0], "latent_image": ["81", 0], "scheduler_func_opt": ["71", 0]}, "class_type": "KSamplerAdvanced //Inspire", "_meta": {"title": "KSamplerAdvanced (inspire)"}}, "81": {"inputs": {"noise_std": 0.30000000000000004, "samples": ["67", 0]}, "class_type": "Latent Noise Injection", "_meta": {"title": "Latent Noise Injection"}}, "82": {"inputs": {"model_name": "8x_NMKD-Typescale_175k.pth"}, "class_type": "UpscaleModelLoader", "_meta": {"title": "Load Upscale Model"}}, "88": {"inputs": {"image": ["188", 0]}, "class_type": "easy imageSize", "_meta": {"title": "ImageSize"}}, "89": {"inputs": {"expression": "a+b", "a": ["88", 0], "b": ["88", 1]}, "class_type": "MathExpression|pysssss", "_meta": {"title": "Math Expression 🐍"}}, "90": {"inputs": {"model_name": "4x-UltraSharp.pth"}, "class_type": "UpscaleModelLoader", "_meta": {"title": "Load Upscale Model"}}, "91": {"inputs": {"comparison": ">=", "a": ["89", 0], "b": ["159", 0]}, "class_type": "SimpleComparison+", "_meta": {"title": "🔧 Simple Comparison"}}, "92": {"inputs": {"upscale_model": ["90", 0], "image": ["188", 0]}, "class_type": "ImageUpscaleWithModel", "_meta": {"title": "Upscale Image (using Model)"}}, "93": {"inputs": {"upscale_method": "lanc<PERSON>s", "megapixels": ["205", 0], "image": ["92", 0]}, "class_type": "ImageScaleToTotalPixels", "_meta": {"title": "Scale Image to Total Pixels"}}, "97": {"inputs": {"unet_name": "flux1_dev.safetensors", "weight_dtype": "default"}, "class_type": "UNETLoader", "_meta": {"title": "Load Diffusion Model"}}, "98": {"inputs": {"vae_name": "Flux_ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "102": {"inputs": {"strength": 0.5500000000000002, "start_percent": ["194", 0], "end_percent": ["198", 0], "positive": ["49", 0], "negative": ["49", 1], "control_net": ["40", 0], "image": ["39", 0], "vae": ["98", 0]}, "class_type": "ControlNetApplyAdvanced", "_meta": {"title": "Apply ControlNet"}}, "103": {"inputs": {"conditioning_1": ["187", 0], "conditioning_2": ["38", 0]}, "class_type": "Conditioning<PERSON><PERSON><PERSON>", "_meta": {"title": "Conditioning (Combine)"}}, "104": {"inputs": {"conditioning": ["1", 0]}, "class_type": "ConditioningZeroOut", "_meta": {"title": "ConditioningZeroOut"}}, "105": {"inputs": {"max_shift": 1.0000000000000002, "base_shift": 1.0000000000000002, "width": ["232", 0], "height": ["232", 1], "model": ["109", 0]}, "class_type": "ModelSamplingFlux", "_meta": {"title": "ModelSamplingFlux"}}, "106": {"inputs": {"boolean": ["91", 0], "on_true": ["204", 0], "on_false": ["93", 0]}, "class_type": "easy ifElse", "_meta": {"title": "If else"}}, "107": {"inputs": {"width": ["232", 0], "height": ["232", 1], "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "Empty Latent Image"}}, "109": {"inputs": {"model": ["165", 0]}, "class_type": "CFGZeroStar", "_meta": {"title": "CFGZeroStar"}}, "111": {"inputs": {"samples": ["60", 0], "vae": ["98", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "114": {"inputs": {"style_model_name": "flex1_redux_siglip2_512.safetensors"}, "class_type": "StyleModelLoader", "_meta": {"title": "Load Style Model"}}, "151": {"inputs": {"clip_name1": "clip_l.safetensors", "clip_name2": "t5xxl_fp8_e4m3fn.safetensors", "type": "flux", "device": "default"}, "class_type": "DualCLIPLoader", "_meta": {"title": "DualCLIPLoader"}}, "152": {"inputs": {"preprocessor": "DepthAnythingV2Preprocessor", "resolution": 1024, "image": ["106", 0]}, "class_type": "AIO_Preprocessor", "_meta": {"title": "AIO Aux Preprocessor"}}, "153": {"inputs": {"control_net_name": "FLUX.1-dev-ControlNet-Union-Pro-2.0.safetensors"}, "class_type": "ControlNetLoader", "_meta": {"title": "Load ControlNet Model"}}, "154": {"inputs": {"control_net_name": "FLUX.1-dev-ControlNet-Union-Pro-2.0.safetensors"}, "class_type": "ControlNetLoader", "_meta": {"title": "Load ControlNet Model"}}, "159": {"inputs": {"value": 3000}, "class_type": "easy int", "_meta": {"title": "Int"}}, "165": {"inputs": {"PowerLoraLoaderHeaderWidget": {"type": "PowerLoraLoaderHeaderWidget"}, "lora_1": {"on": true, "lora": "ASTRA_FLUX_LORA\\建筑-商办综合立面\\ASTRA_Flux_OC_Vbeta-2.safetensors", "strength": 0.45}, "lora_2": {"on": true, "lora": "FLUX\\Flux-Function\\FLUX.1-Turbo-Alpha.safetensors", "strength": 1}, "➕ Add Lora": "", "model": ["97", 0], "clip": ["151", 0]}, "class_type": "<PERSON> Lora <PERSON> (rgthree)", "_meta": {"title": "<PERSON> Lora <PERSON> (rgthree)"}}, "181": {"inputs": {"strength": 1.0000000000000002, "conditioning": ["16", 0]}, "class_type": "ConditioningSetAreaStrength", "_meta": {"title": "ConditioningSetAreaStrength"}}, "185": {"inputs": {"brightness": 0, "contrast": 1.1500000000000001, "saturation": 1.1700000000000002, "sharpness": 1, "blur": 0, "gaussian_blur": 0, "edge_enhance": 0, "detail_enhance": "false", "image": ["111", 0]}, "class_type": "Image Filter Adjustments", "_meta": {"title": "Image Filter Adjustments"}}, "187": {"inputs": {"start": 0, "end": ["198", 0], "conditioning": ["201", 0]}, "class_type": "ConditioningSetTimestepRange", "_meta": {"title": "ConditioningSetTimestepRange"}}, "188": {"inputs": {"base64_data": "", "image_output": "Preview", "save_prefix": "ComfyUI"}, "class_type": "easy loadImageBase64", "_meta": {"title": "controlnet_image_input"}}, "192": {"inputs": {"value": 0.7800000000000001}, "class_type": "easy float", "_meta": {"title": "CN-控制部分结束时机"}}, "193": {"inputs": {"expression": "a*b", "a": ["206", 0], "b": ["57", 0]}, "class_type": "MathExpression|pysssss", "_meta": {"title": "Math Expression 🐍"}}, "194": {"inputs": {"value": 0.5000000000000001}, "class_type": "easy float", "_meta": {"title": "CN-深度/线稿切换时机"}}, "195": {"inputs": {"expression": "a*b", "a": ["206", 0], "b": ["192", 0]}, "class_type": "MathExpression|pysssss", "_meta": {"title": "Math Expression 🐍"}}, "197": {"inputs": {"comparison": "<=", "a": ["195", 1], "b": ["194", 0]}, "class_type": "SimpleComparison+", "_meta": {"title": "🔧 Simple Comparison"}}, "198": {"inputs": {"boolean": ["197", 0], "on_true": ["194", 0], "on_false": ["195", 1]}, "class_type": "easy ifElse", "_meta": {"title": "If else"}}, "200": {"inputs": {"strength": 1.0000000000000002, "conditioning": ["181", 0]}, "class_type": "ConditioningSetAreaStrength", "_meta": {"title": "ConditioningSetAreaStrength"}}, "201": {"inputs": {"strength": 1.0000000000000002, "conditioning": ["102", 0]}, "class_type": "ConditioningSetAreaStrength", "_meta": {"title": "ConditioningSetAreaStrength"}}, "204": {"inputs": {"upscale_method": "lanc<PERSON>s", "megapixels": ["205", 0], "image": ["188", 0]}, "class_type": "ImageScaleToTotalPixels", "_meta": {"title": "Scale Image to Total Pixels"}}, "205": {"inputs": {"value": 2.0000000000000004}, "class_type": "easy float", "_meta": {"title": "首采分辨率大小设置（百万像素单位）"}}, "206": {"inputs": {"value": 1.0000000000000002}, "class_type": "easy float", "_meta": {"title": "CN全局控制强度"}}, "207": {"inputs": {"value": 67977614402230, "mode": "randomize", "action": "randomize", "last_seed": 396664168649897}, "class_type": "easy globalSeed", "_meta": {"title": "EasyGlobalSeed"}}, "210": {"inputs": {"guidance": 3.5, "conditioning": ["211", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "211": {"inputs": {"text": ["230", 0], "clip": ["165", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "230": {"inputs": {"prompt": "An architectural photography of a luxury high-rise apartment, clean lines, copper facade, glazed window with beautiful reflections, minimalist, highly detailed, urban skyline and clear sky as the backdrop, trees all around"}, "class_type": "CR Prompt Text", "_meta": {"title": "prompt_input_01"}}, "232": {"inputs": {"image": ["106", 0]}, "class_type": "GetImageSize+", "_meta": {"title": "🔧 Get Image Size"}}, "235": {"inputs": {"anything": ["185", 0]}, "class_type": "easy cleanGpuUsed", "_meta": {"title": "Clean VRAM Used"}}, "236": {"inputs": {"method": "hm-mvgd-hm", "strength": 0.6500000000000001, "image_ref": ["185", 0], "image_target": ["73", 0]}, "class_type": "ColorMatch", "_meta": {"title": "Color Match"}}, "237": {"inputs": {"anything": ["236", 0]}, "class_type": "easy cleanGpuUsed", "_meta": {"title": "Clean VRAM Used"}}, "243": {"inputs": {"base64_data": "", "image_output": "Preview", "save_prefix": "ComfyUI"}, "class_type": "easy loadImageBase64", "_meta": {"title": "redux_image_input"}}, "244": {"inputs": {"width": 2048, "height": 2048, "interpolation": "nearest", "method": "keep proportion", "condition": "always", "multiple_of": 0, "image": ["243", 0]}, "class_type": "ImageResize+", "_meta": {"title": "🔧 Image Resize"}}}