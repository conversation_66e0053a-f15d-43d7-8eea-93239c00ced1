"""
Flux LoRA 集成器 V2 - 简洁高效版本

设计原则：
1. 简单明确的节点连接逻辑
2. 严格的数据类型验证
3. 清晰的错误处理
4. 支持多种 Flux 工作流类型
"""

import logging
import copy
from typing import Dict, List, Optional, Any, Tuple

from .flux_workflow_models import LoRAConfig, FluxParameters, LoRACategory
from ...workers.shared.shared_lora_manager import SharedLoraManager


class LoRAIntegrationV2:
    """Flux LoRA 集成器 V2 - 重新设计的简洁版本"""
    
    def __init__(self):
        # 使用主应用的logger，确保日志能正确输出
        self.logger = logging.getLogger('langbot')
        self.lora_manager = SharedLoraManager()
        
    async def apply_loras_to_workflow(self, workflow_data: Dict[str, Any], loras: List[LoRAConfig]) -> Tuple[Dict[str, Any], str]:
        """
        将 LoRA 模型应用到工作流中

        Args:
            workflow_data: 原始工作流数据
            loras: LoRA 配置列表

        Returns:
            Tuple[Dict[str, Any], str]: (更新后的工作流数据, 状态消息)
        """
        # 如果没有LoRA模型，直接返回原始工作流，避免破坏现有结构
        if not loras:
            self.logger.info("没有LoRA模型需要应用")
            return workflow_data, "✅ 工作流准备完成（无LoRA）"
            
        try:
            # 深拷贝工作流数据，避免污染原始数据
            updated_workflow = copy.deepcopy(workflow_data)
            
            # 验证工作流数据完整性
            self._validate_workflow_structure(updated_workflow)
            
            # 分析工作流类型和结构
            workflow_info = self._analyze_workflow_structure(updated_workflow)
            
            # 应用 LoRA 模型
            success_count = await self._apply_loras_to_structure(updated_workflow, loras, workflow_info)
            
            if success_count == 0:
                return workflow_data, "⚠️ 没有成功应用任何LoRA模型，使用原始工作流"
            
            # 生成成功消息
            lora_names = [lora.name for lora in loras[:success_count]]
            success_message = f"✅ 成功应用 {success_count} 个LoRA模型: {', '.join(lora_names[:2])}{'...' if len(lora_names) > 2 else ''}"
            
            return updated_workflow, success_message
            
        except Exception as e:
            self.logger.error(f"LoRA应用失败: {e}")
            error_message = f"⚠️ LoRA模型应用失败，已降级为标准工作流。错误: {str(e)}"
            return workflow_data, error_message
    
    def _validate_workflow_structure(self, workflow_data: Dict[str, Any]):
        """验证工作流数据结构的完整性"""
        if not isinstance(workflow_data, dict):
            raise ValueError(f"工作流数据必须是字典类型，当前类型: {type(workflow_data)}")
        
        # 检查每个节点的数据类型
        for node_id, node_data in workflow_data.items():
            if not isinstance(node_data, dict):
                raise ValueError(f"节点 {node_id} 的数据类型错误: {type(node_data)}, 值: {node_data}")
            
            if "class_type" not in node_data:
                raise ValueError(f"节点 {node_id} 缺少 class_type 字段")
            
            if not isinstance(node_data.get("inputs", {}), dict):
                raise ValueError(f"节点 {node_id} 的 inputs 字段必须是字典类型")
    
    def _analyze_workflow_structure(self, workflow_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析工作流结构，识别关键节点"""
        workflow_info = {
            "type": "unknown",
            "unet_loader": None,
            "clip_loader": None,
            "sampler": None,
            "existing_loras": [],
            "model_chain": [],
            "clip_chain": []
        }
        
        # 查找关键节点
        for node_id, node_data in workflow_data.items():
            class_type = node_data.get("class_type", "")
            
            # UNet 加载器
            if class_type == "UNETLoader":
                workflow_info["unet_loader"] = node_id
                
            # CLIP 加载器
            elif class_type == "DualCLIPLoader":
                workflow_info["clip_loader"] = node_id
                
            # 采样器
            elif "Sampler" in class_type or "KSampler" in class_type:
                workflow_info["sampler"] = node_id
                
            # 现有的 LoRA 节点
            elif "Lora" in class_type:
                workflow_info["existing_loras"].append(node_id)
        
        # 确定工作流类型
        if "ControlNet" in str(workflow_data):
            workflow_info["type"] = "controlnet"
        elif "CLIPVision" in str(workflow_data):
            workflow_info["type"] = "redux"
        else:
            workflow_info["type"] = "default"
        
        self.logger.info(f"工作流分析结果: {workflow_info}")
        return workflow_info
    
    async def _apply_loras_to_structure(self, workflow_data: Dict[str, Any], loras: List[LoRAConfig], workflow_info: Dict[str, Any]) -> int:
        """根据工作流结构应用 LoRA 模型"""
        if not workflow_info["unet_loader"]:
            raise ValueError("未找到 UNETLoader 节点，无法应用 LoRA")

        # 清理现有的 LoRA 节点
        self._remove_existing_loras(workflow_data, workflow_info["existing_loras"])

        # 构建 LoRA 链
        success_count = await self._build_lora_chain(workflow_data, loras, workflow_info)

        return success_count
    
    def _remove_existing_loras(self, workflow_data: Dict[str, Any], existing_loras: List[str]):
        """移除现有的 LoRA 节点"""
        for lora_id in existing_loras:
            if lora_id in workflow_data:
                del workflow_data[lora_id]
                self.logger.info(f"移除现有LoRA节点: {lora_id}")
    
    async def _build_lora_chain(self, workflow_data: Dict[str, Any], loras: List[LoRAConfig], workflow_info: Dict[str, Any]) -> int:
        """构建 LoRA 节点链"""
        unet_loader_id = workflow_info["unet_loader"]
        clip_loader_id = workflow_info["clip_loader"]

        # 获取ComfyUI可用的LoRA列表
        available_loras = await self._get_comfyui_lora_list()

        # 当前模型和CLIP的输出
        current_model_output = [unet_loader_id, 0]
        current_clip_output = [clip_loader_id, 0] if clip_loader_id else None

        success_count = 0

        for i, lora in enumerate(loras):
            lora_node_id = f"lora_{i + 1}"

            # 创建 LoRA 节点
            lora_node = await self._create_lora_node(lora, current_model_output, current_clip_output, available_loras)

            if lora_node:
                workflow_data[lora_node_id] = lora_node

                # 更新输出连接
                current_model_output = [lora_node_id, 0]
                if current_clip_output:
                    current_clip_output = [lora_node_id, 1]

                success_count += 1
                self.logger.info(f"成功添加LoRA节点: {lora_node_id} ({lora.name})")
            else:
                self.logger.warning(f"跳过无法匹配的LoRA: {lora.name} ({lora.filename})")

        # 更新依赖节点的连接
        if success_count > 0:
            self._update_dependent_connections(workflow_data, workflow_info, current_model_output, current_clip_output)

        return success_count
    
    async def _create_lora_node(self, lora: LoRAConfig, model_input: List, clip_input: Optional[List], available_loras: List[str] = None) -> Optional[Dict[str, Any]]:
        """创建单个 LoRA 节点"""
        try:
            # 使用智能匹配找到ComfyUI中的实际文件名
            matched_filename = self._find_matching_comfyui_filename(lora, available_loras or [])

            if not matched_filename:
                self.logger.warning(f"🔧 [DEBUG] 无法为LoRA找到匹配的文件名: {lora.name} ({lora.filename})")
                # 如果有可用列表但没找到匹配，跳过这个LoRA
                if available_loras:
                    return None
                # 如果没有可用列表，使用原始文件名
                matched_filename = lora.filename

            self.logger.info(f"🔧 [DEBUG] 创建LoRA节点: {lora.name}, 原始文件名: {lora.filename}, 匹配文件名: {matched_filename}")

            if clip_input:
                # 标准 LoRA 节点（同时处理模型和CLIP）
                return {
                    "class_type": "LoraLoader",
                    "inputs": {
                        "model": model_input,
                        "clip": clip_input,
                        "lora_name": matched_filename,
                        "strength_model": round(lora.weight, 3),
                        "strength_clip": round(lora.weight, 3)
                    },
                    "_meta": {
                        "title": f"LoRA: {lora.name}"
                    }
                }
            else:
                # 仅模型 LoRA 节点
                return {
                    "class_type": "LoraLoaderModelOnly",
                    "inputs": {
                        "model": model_input,
                        "lora_name": matched_filename,
                        "strength_model": round(lora.weight, 3)
                    },
                    "_meta": {
                        "title": f"LoRA: {lora.name}"
                    }
                }
        except Exception as e:
            self.logger.error(f"创建LoRA节点失败: {e}")
            return None

    async def _get_comfyui_lora_list(self) -> List[str]:
        """获取ComfyUI中实际可用的LoRA列表"""
        try:
            import aiohttp
            # 使用ComfyUI的API获取可用的LoRA列表
            api_url = "http://127.0.0.1:8188/object_info/LoraLoader"

            async with aiohttp.ClientSession() as session:
                async with session.get(api_url) as response:
                    if response.status == 200:
                        data = await response.json()
                        # 提取LoRA文件名列表
                        lora_list = data.get("LoraLoader", {}).get("input", {}).get("required", {}).get("lora_name", [[], {}])[0]
                        self.logger.info(f"🔧 [DEBUG] ComfyUI可用LoRA数量: {len(lora_list)}")
                        return lora_list
                    else:
                        self.logger.warning(f"获取ComfyUI LoRA列表失败: HTTP {response.status}")
                        return []
        except Exception as e:
            self.logger.error(f"查询ComfyUI LoRA列表失败: {e}")
            return []

    def _find_matching_comfyui_filename(self, lora_config: LoRAConfig, available_loras: List[str]) -> Optional[str]:
        """
        为LoRA配置找到在ComfyUI中匹配的实际文件名

        Args:
            lora_config: LoRA配置对象
            available_loras: ComfyUI中可用的LoRA文件名列表

        Returns:
            Optional[str]: 匹配的文件名，如果没找到返回None
        """
        if not available_loras:
            # 如果无法获取ComfyUI列表，使用配置文件中的文件名
            self.logger.warning(f"🔧 [DEBUG] 无法获取ComfyUI LoRA列表，使用配置文件名: {lora_config.filename}")
            return lora_config.filename

        # 获取原始文件名
        import os
        original_filename = os.path.basename(lora_config.filename)

        # 1. 首先尝试精确匹配配置文件中的文件名
        if original_filename in available_loras:
            self.logger.info(f"🔧 [DEBUG] 配置文件文件名精确匹配: {original_filename}")
            return original_filename

        # 2. 生成候选文件名并尝试匹配
        candidates = self._generate_filename_candidates(original_filename, lora_config)

        # 3. 精确匹配候选文件名
        for candidate in candidates:
            if candidate in available_loras:
                self.logger.info(f"🔧 [DEBUG] 候选文件名精确匹配: {candidate}")
                self._suggest_config_fix(lora_config, candidate)
                return candidate

        # 4. 部分匹配（基于文件名相似度）
        for candidate in candidates:
            for available in available_loras:
                if self._is_filename_similar(candidate, available):
                    self.logger.info(f"🔧 [DEBUG] 文件名相似匹配: {available} (原始: {candidate})")
                    self._suggest_config_fix(lora_config, available)
                    return available

        # 5. 基于LoRA名称的智能匹配
        lora_name_lower = lora_config.name.lower()
        for available in available_loras:
            available_lower = available.lower()
            if lora_name_lower in available_lower or available_lower in lora_name_lower:
                self.logger.info(f"🔧 [DEBUG] 基于LoRA名称匹配: {available} (LoRA名称: {lora_config.name})")
                self._suggest_config_fix(lora_config, available)
                return available

        # 6. 基于触发词的匹配
        for trigger_word in lora_config.trigger_words:
            trigger_lower = trigger_word.lower()
            for available in available_loras:
                available_lower = available.lower()
                if trigger_lower in available_lower:
                    self.logger.info(f"🔧 [DEBUG] 基于触发词匹配: {available} (触发词: {trigger_word})")
                    self._suggest_config_fix(lora_config, available)
                    return available

        # 7. 如果都没找到，记录详细信息并建议修正
        self.logger.warning(f"🔧 [DEBUG] 未找到匹配的ComfyUI文件: {lora_config.name} ({original_filename})")
        self.logger.warning(f"🔧 [DEBUG] 建议检查配置文件或运行: python3 discover_lora_models.py")
        self.logger.warning(f"🔧 [DEBUG] 可用文件示例: {available_loras[:5]}...")
        return None

    def _suggest_config_fix(self, lora_config: LoRAConfig, correct_filename: str):
        """建议配置文件修正"""
        if lora_config.filename != correct_filename:
            self.logger.info(f"💡 [建议] 配置文件修正: {lora_config.name}")
            self.logger.info(f"   当前文件名: {lora_config.filename}")
            self.logger.info(f"   建议文件名: {correct_filename}")
            self.logger.info(f"   修正命令: python3 analyze_lora_models.py update \"{lora_config.name}\" --filename \"{correct_filename}\"")

    def _generate_filename_candidates(self, filename: str, lora_config: LoRAConfig) -> List[str]:
        """生成文件名候选列表"""
        candidates = []

        # 基于原始文件名的变体
        candidates.extend([
            filename,
            filename.replace('.safetensors', ''),
            filename + '.safetensors' if not filename.endswith('.safetensors') else filename,
        ])

        # 基于LoRA名称的变体
        lora_name = lora_config.name
        candidates.extend([
            lora_name,
            lora_name + '.safetensors',
            lora_name.replace(' ', '_'),
            lora_name.replace(' ', '_') + '.safetensors',
            lora_name.replace(' ', '-'),
            lora_name.replace(' ', '-') + '.safetensors',
        ])

        # 移除重复项
        return list(set(candidates))

    def _is_filename_similar(self, name1: str, name2: str) -> bool:
        """检查两个文件名是否相似"""
        # 移除扩展名进行比较
        base1 = name1.replace('.safetensors', '').replace('.ckpt', '').replace('.pt', '')
        base2 = name2.replace('.safetensors', '').replace('.ckpt', '').replace('.pt', '')

        # 标准化（移除特殊字符，转小写）
        import re
        base1_clean = re.sub(r'[_\-\s]+', '', base1.lower())
        base2_clean = re.sub(r'[_\-\s]+', '', base2.lower())

        # 检查相似度
        return (base1_clean in base2_clean or base2_clean in base1_clean or
                base1_clean == base2_clean)
    
    def _update_dependent_connections(self, workflow_data: Dict[str, Any], workflow_info: Dict[str, Any],
                                    final_model_output: List, final_clip_output: Optional[List]):
        """更新依赖节点的连接"""
        unet_loader_id = workflow_info["unet_loader"]
        clip_loader_id = workflow_info["clip_loader"]
        existing_loras = workflow_info["existing_loras"]

        for node_id, node_data in workflow_data.items():
            if not isinstance(node_data, dict) or "inputs" not in node_data:
                continue

            inputs = node_data["inputs"]
            updated = False

            # 检查所有输入字段
            for input_name, input_value in inputs.items():
                if isinstance(input_value, list) and len(input_value) >= 2:
                    source_node_id = input_value[0]

                    # 更新直接连接到UNETLoader的模型连接
                    if source_node_id == unet_loader_id and input_name == "model":
                        inputs[input_name] = final_model_output
                        self.logger.info(f"更新节点 {node_id} 的模型连接: {input_name}")
                        updated = True

                    # 更新直接连接到CLIPLoader的CLIP连接
                    elif final_clip_output and source_node_id == clip_loader_id and input_name == "clip":
                        inputs[input_name] = final_clip_output
                        self.logger.info(f"更新节点 {node_id} 的CLIP连接: {input_name}")
                        updated = True

                    # 更新连接到被删除LoRA节点的连接
                    elif source_node_id in existing_loras:
                        # 根据输出索引确定连接类型
                        output_index = input_value[1]
                        if output_index == 0:  # 模型输出
                            inputs[input_name] = final_model_output
                            self.logger.info(f"更新节点 {node_id} 的连接 {input_name}: 从删除的LoRA节点 {source_node_id} 重定向到最终模型输出")
                            updated = True
                        elif output_index == 1 and final_clip_output:  # CLIP输出
                            inputs[input_name] = final_clip_output
                            self.logger.info(f"更新节点 {node_id} 的连接 {input_name}: 从删除的LoRA节点 {source_node_id} 重定向到最终CLIP输出")
                            updated = True

            if updated:
                self.logger.info(f"✅ 节点 {node_id} 的连接已更新")

    async def select_loras_for_prompt(self, params: FluxParameters, max_loras: int = 3,
                                    use_civitai: bool = False, civitai_query: str = "") -> List[LoRAConfig]:
        """
        根据提示词和参数选择合适的LoRA模型

        Args:
            params: Flux参数
            max_loras: 最大LoRA数量
            use_civitai: 是否使用Civitai搜索
            civitai_query: Civitai搜索关键词

        Returns:
            List[LoRAConfig]: 选择的LoRA配置列表
        """
        try:
            selected_loras = []

            # 第一步：本地模型匹配
            search_query = civitai_query if civitai_query else params.prompt
            self.logger.info(f"🔧 [DEBUG] 搜索查询: '{search_query}'")

            # 检查LoRA管理器状态
            total_models = len(self.lora_manager.lora_models)
            active_models = len([m for m in self.lora_manager.lora_models.values() if m.is_active])
            self.logger.info(f"🔧 [DEBUG] LoRA管理器状态: 总模型数={total_models}, 活跃模型数={active_models}")

            selected_models = self.lora_manager.get_models_by_trigger(search_query)
            self.logger.info(f"🔧 [DEBUG] 匹配到的模型数量: {len(selected_models)}")
            for model in selected_models:
                self.logger.info(f"🔧 [DEBUG] 匹配模型: {model.name} (触发词: {model.trigger_words})")

            # 动态决定LoRA数量
            optimal_lora_count = self._determine_optimal_lora_count(selected_models, search_query)
            self.logger.info(f"根据匹配质量，选择 {optimal_lora_count} 个LoRA模型")

            # 如果启用Civitai且本地模型不足，尝试搜索下载
            if use_civitai and len(selected_models) < optimal_lora_count:
                self.logger.info(f"本地模型不足，从Civitai搜索: {search_query}")
                await self._search_and_download_civitai_models(search_query, optimal_lora_count - len(selected_models))
                # 重新获取模型（可能包含新下载的）
                selected_models = self.lora_manager.get_models_by_trigger(search_query)

            # 转换为LoRAConfig格式，限制数量
            for model in selected_models[:optimal_lora_count]:
                lora_config = LoRAConfig(
                    name=model.name,
                    filename=model.filename,
                    weight=model.weight,
                    category=self._map_category(model.category.value),
                    trigger_words=model.trigger_words,
                    description=model.description,
                    file_path=model.file_path,
                    is_local=model.is_local,
                    is_active=model.is_active
                )

                # 设置Civitai信息
                if model.civitai_id:
                    lora_config.civitai_id = model.civitai_id
                if model.civitai_url:
                    lora_config.civitai_url = model.civitai_url
                if model.rating is not None:
                    lora_config.rating = float(model.rating)
                if model.downloads is not None:
                    lora_config.downloads = int(model.downloads)

                selected_loras.append(lora_config)

            # 如果没有找到合适的LoRA，添加默认的细节增强LoRA
            if not selected_loras:
                default_lora = self._get_default_detail_lora()
                if default_lora:
                    selected_loras.append(default_lora)

            self.logger.info(f"为提示词选择了 {len(selected_loras)} 个LoRA模型")
            for lora in selected_loras:
                self.logger.info(f"  - {lora.name} (权重: {lora.weight}, 分类: {lora.category.value})")

            return selected_loras

        except Exception as e:
            self.logger.error(f"选择LoRA模型失败: {e}")
            # 返回默认LoRA
            default_lora = self._get_default_detail_lora()
            return [default_lora] if default_lora else []

    def optimize_lora_weights(self, loras: List[LoRAConfig], quality_level: str) -> List[LoRAConfig]:
        """
        根据质量级别优化LoRA权重

        Args:
            loras: LoRA配置列表
            quality_level: 质量级别

        Returns:
            List[LoRAConfig]: 优化后的LoRA配置列表
        """
        if not loras:
            return []

        # 权重调整系数
        weight_multipliers = {
            "draft": 0.6,      # 草稿模式：降低权重
            "standard": 0.8,   # 标准模式：适中权重
            "high": 1.0,       # 高质量：正常权重
            "ultra": 1.2       # 超高质量：增强权重
        }

        multiplier = weight_multipliers.get(quality_level, 1.0)
        optimized_loras = []

        for lora in loras:
            optimized_lora = LoRAConfig(
                name=lora.name,
                filename=lora.filename,
                weight=min(1.0, lora.weight * multiplier),  # 确保不超过1.0
                category=lora.category,
                trigger_words=lora.trigger_words,
                description=lora.description,
                file_path=lora.file_path,
                civitai_id=lora.civitai_id,
                civitai_url=lora.civitai_url,
                rating=lora.rating,
                downloads=lora.downloads,
                is_local=lora.is_local,
                is_active=lora.is_active
            )
            optimized_loras.append(optimized_lora)

        self.logger.info(f"为质量级别 '{quality_level}' 优化LoRA权重 (系数: {multiplier})")
        return optimized_loras

    def _determine_optimal_lora_count(self, selected_models: List, search_query: str) -> int:
        """
        根据匹配质量和用户需求动态决定LoRA数量

        Args:
            selected_models: 匹配的模型列表
            search_query: 搜索查询

        Returns:
            int: 最优的LoRA数量
        """
        if not selected_models:
            return 1  # 至少返回一个默认LoRA

        # 基础规则
        base_count = min(len(selected_models), 3)

        # 根据查询复杂度调整
        query_words = search_query.split()
        if len(query_words) > 10:  # 复杂查询
            base_count = min(base_count + 1, 4)
        elif len(query_words) < 5:  # 简单查询
            base_count = max(base_count - 1, 1)

        # 根据模型质量调整
        high_quality_count = sum(1 for model in selected_models if (getattr(model, 'rating', None) or 0) > 4.0)
        if high_quality_count >= 2:
            base_count = min(base_count + 1, 4)

        return base_count

    def _get_default_detail_lora(self) -> Optional[LoRAConfig]:
        """
        获取默认的细节增强LoRA

        Returns:
            Optional[LoRAConfig]: 默认LoRA配置，如果没有找到则返回None
        """
        try:
            # 尝试获取一个通用的细节增强LoRA
            all_models = list(self.lora_manager.lora_models.values())
            self.logger.info(f"🔧 [DEBUG] 默认LoRA搜索: 总模型数={len(all_models)}")

            # 优先选择本地模型
            local_models = [m for m in all_models if m.is_local and m.is_active]
            self.logger.info(f"🔧 [DEBUG] 本地活跃模型数: {len(local_models)}")

            if local_models:
                # 选择第一个本地模型作为默认
                model = local_models[0]
                self.logger.info(f"🔧 [DEBUG] 选择默认LoRA: {model.name}")
                return LoRAConfig(
                    name=model.name,
                    filename=model.filename,
                    weight=0.7,  # 默认权重
                    category=self._map_category(model.category.value),
                    trigger_words=model.trigger_words,
                    description=model.description,
                    file_path=model.file_path,
                    is_local=model.is_local,
                    is_active=model.is_active
                )

            self.logger.warning("没有找到可用的默认LoRA模型")
            return None

        except Exception as e:
            self.logger.error(f"获取默认LoRA失败: {e}")
            import traceback
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")
            return None

    def _map_category(self, category_str: str) -> LoRACategory:
        """
        将字符串分类映射到LoRACategory枚举

        Args:
            category_str: 分类字符串

        Returns:
            LoRACategory: 映射后的分类枚举
        """
        category_mapping = {
            "character": LoRACategory.CHARACTER,
            "style": LoRACategory.STYLE,
            "concept": LoRACategory.CONCEPT,
            "clothing": LoRACategory.CLOTHING,
            "pose": LoRACategory.POSE,
            "background": LoRACategory.BACKGROUND,
            "effect": LoRACategory.EFFECT,
            "tool": LoRACategory.TOOL,
            "other": LoRACategory.OTHER
        }

        return category_mapping.get(category_str.lower(), LoRACategory.OTHER)

    async def _search_and_download_civitai_models(self, query: str, max_models: int = 2):
        """
        从Civitai搜索并下载模型

        Args:
            query: 搜索关键词
            max_models: 最大下载模型数量
        """
        try:
            self.logger.info(f"从Civitai搜索模型: {query} (最多 {max_models} 个)")

            # 更新模型信息（搜索）
            result = await self.lora_manager.update_from_civitai(query=query, limit=max_models * 2)
            self.logger.info(f"Civitai搜索结果: {result}")

        except Exception as e:
            self.logger.error(f"Civitai搜索失败: {e}")

    def get_lora_statistics(self) -> Dict[str, Any]:
        """
        获取LoRA统计信息

        Returns:
            Dict[str, Any]: LoRA统计信息
        """
        try:
            all_models = list(self.lora_manager.lora_models.values())
            local_models = [m for m in all_models if m.is_local]
            active_models = [m for m in all_models if m.is_active]

            return {
                'total_models': len(all_models),
                'local_models': len(local_models),
                'active_models': len(active_models),
                'remote_models': len(all_models) - len(local_models),
                'categories': self._get_category_distribution(all_models),
                'average_rating': self._calculate_average_rating(all_models)
            }
        except Exception as e:
            self.logger.error(f"获取LoRA统计失败: {e}")
            return {}

    def _get_category_distribution(self, models: List) -> Dict[str, int]:
        """获取分类分布"""
        distribution = {}
        for model in models:
            category = getattr(model, 'category', None)
            if category:
                category_name = category.value if hasattr(category, 'value') else str(category)
                distribution[category_name] = distribution.get(category_name, 0) + 1
        return distribution

    def _calculate_average_rating(self, models: List) -> float:
        """计算平均评分"""
        ratings = [getattr(model, 'rating', 0) for model in models if (getattr(model, 'rating', None) or 0) > 0]
        return sum(ratings) / len(ratings) if ratings else 0.0


# 全局实例
_lora_integration_v2: Optional[LoRAIntegrationV2] = None


def get_lora_integration_v2() -> LoRAIntegrationV2:
    """获取 LoRA 集成器 V2 实例"""
    global _lora_integration_v2
    if _lora_integration_v2 is None:
        _lora_integration_v2 = LoRAIntegrationV2()
    return _lora_integration_v2
