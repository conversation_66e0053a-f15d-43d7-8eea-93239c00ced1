#!/usr/bin/env python3
"""
[二次开发] LoRA模型自动发现工具

自动扫描ComfyUI的loras文件夹，发现新的.safetensors文件并添加到配置中

开发说明：
- 此文件为二次开发代码，不属于langbot原生代码
- 功能：自动发现和注册新的LoRA模型
- 维护者：开发团队
- 最后更新：2024-12-20
- 相关任务：DEV-05 配置模块实现
- 依赖关系：依赖shared_lora_manager和现有配置文件
"""

import asyncio
import sys
import os
import json
import argparse
import re
from pathlib import Path
from typing import List, Dict, Set, Optional
from datetime import datetime

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

from pkg.workers.shared.shared_lora_manager import SharedLoraManager, LoraCategory


class LoraAutoDiscovery:
    """LoRA模型自动发现器"""
    
    def __init__(self):
        self.lora_dir = "/home/<USER>/Workspace/ComfyUI/models/loras"
        self.config_file = "config/lora_models.json"
        self.manager = SharedLoraManager()
        self.manager.initialize()
        
        # 智能分类关键词映射（更全面的关键词）
        self.category_keywords = {
            LoraCategory.ARCHITECTURE: [
                "建筑", "architecture", "building", "house", "interior", "室内", 
                "exterior", "外观", "facade", "立面", "modern", "现代", "classical", "古典",
                "villa", "别墅", "hotel", "酒店", "office", "办公室", "commercial", "商业",
                "astra", "oc", "outdoor", "indoor", "bedroom", "living", "residential", "住宅",
                "curtain", "wall", "shikumen", "石库门", "neochinese", "新中式", "minimalism", "极简"
            ],
            LoraCategory.PORTRAIT: [
                "portrait", "人像", "face", "面部", "person", "人物", "character", "角色",
                "girl", "女孩", "boy", "男孩", "woman", "女人", "man", "男人", "beauty", "美女"
            ],
            LoraCategory.LANDSCAPE: [
                "landscape", "风景", "nature", "自然", "scenery", "景观", "garden", "花园",
                "forest", "森林", "mountain", "山", "sea", "海", "sky", "天空", "outdoor", "户外",
                "plant", "植物", "aerial", "航拍", "photography", "摄影", "rsd", "landscape"
            ],
            LoraCategory.ANIME: [
                "anime", "动漫", "manga", "漫画", "niji", "二次元", "cartoon", "卡通",
                "chibi", "Q版", "kawaii", "可爱", "moe", "萌"
            ],
            LoraCategory.DETAIL: [
                "detail", "细节", "enhance", "增强", "quality", "质量", "sharp", "锐利",
                "ultra", "ultra", "high", "高清", "resolution", "分辨率", "fine", "精细"
            ],
            LoraCategory.STYLE: [
                "style", "风格", "art", "艺术", "realistic", "写实", "photorealistic", "真实",
                "minimalist", "极简", "modern", "现代", "classical", "古典", "vintage", "复古"
            ],
            LoraCategory.OBJECT: [
                "object", "物体", "item", "物品", "furniture", "家具", "car", "汽车",
                "weapon", "武器", "tool", "工具", "accessory", "配饰"
            ]
        }
        
        # 优先级关键词
        self.priority_keywords = [
            "detail", "enhance", "quality", "ultra", "pro", "professional", "master",
            "细节", "增强", "质量", "专业", "大师", "pro", "ultra"
        ]
    
    def scan_lora_directory(self) -> List[Path]:
        """扫描LoRA目录，返回所有.safetensors文件"""
        lora_path = Path(self.lora_dir)
        if not lora_path.exists():
            print(f"❌ LoRA目录不存在: {self.lora_dir}")
            return []
        
        safetensors_files = []
        for file_path in lora_path.rglob("*.safetensors"):
            safetensors_files.append(file_path)
        
        print(f"🔍 扫描到 {len(safetensors_files)} 个.safetensors文件")
        return safetensors_files
    
    def get_existing_models(self) -> Set[str]:
        """获取已配置的模型文件名集合"""
        existing_files = set()
        for model in self.manager.lora_models.values():
            existing_files.add(model.filename)
        return existing_files
    
    def extract_model_info(self, file_path: Path) -> Dict:
        """从文件名提取模型信息"""
        filename = file_path.name
        name = file_path.stem  # 去掉扩展名
        
        # 尝试从Civitai获取真实信息
        civitai_info = self._get_civitai_info(name, filename)
        
        if civitai_info:
            # 使用Civitai信息
            category = civitai_info.get('category', self._classify_model(name, filename))
            trigger_words = civitai_info.get('trigger_words', self._generate_trigger_words(name, filename))
            description = civitai_info.get('description', f"自动发现的Lora模型: {filename}")
            is_priority = civitai_info.get('is_priority', self._is_priority_model(name, filename))
            civitai_id = civitai_info.get('civitai_id')
            civitai_url = civitai_info.get('civitai_url')
            rating = civitai_info.get('rating')
            downloads = civitai_info.get('downloads')
        else:
            # 使用本地分析
            category = self._classify_model(name, filename)
            trigger_words = self._generate_trigger_words(name, filename)
            description = f"自动发现的Lora模型: {filename}"
            is_priority = self._is_priority_model(name, filename)
            civitai_id = None
            civitai_url = None
            rating = None
            downloads = None
        
        return {
            "name": name,
            "filename": filename,
            "file_path": str(file_path),
            "category": category,
            "trigger_words": trigger_words,
            "description": description,
            "is_priority": is_priority,
            "civitai_id": civitai_id,
            "civitai_url": civitai_url,
            "rating": rating,
            "downloads": downloads
        }
    
    def _classify_model(self, name: str, filename: str) -> LoraCategory:
        """智能分类模型"""
        text = (name + " " + filename).lower()
        
        # 计算每个分类的匹配分数
        category_scores = {}
        for category, keywords in self.category_keywords.items():
            score = 0
            for keyword in keywords:
                if keyword.lower() in text:
                    score += 1
            category_scores[category] = score
        
        # 返回得分最高的分类
        if category_scores:
            best_category = max(category_scores.items(), key=lambda x: x[1])
            if best_category[1] > 0:
                return best_category[0]
        
        return LoraCategory.OTHER
    
    def _get_civitai_info(self, name: str, filename: str) -> Optional[Dict]:
        """尝试从Civitai获取模型信息"""
        try:
            # 这里可以集成Civitai API搜索
            # 暂时返回None，表示使用本地分析
            # 后续可以实现真实的Civitai搜索
            return None
        except Exception as e:
            print(f"⚠️ 获取Civitai信息失败: {e}")
            return None
    
    def _generate_trigger_words(self, name: str, filename: str) -> List[str]:
        """生成触发词"""
        trigger_words = []
        text = (name + " " + filename).lower()
        
        # 从文件名中提取关键词
        words = re.findall(r'[a-zA-Z]+', text)
        chinese_words = re.findall(r'[\u4e00-\u9fff]+', text)
        
        # 添加英文关键词（过滤掉常见无意义词）
        common_words = {'lora', 'flux', 'model', 'v1', 'v2', 'v3', 'beta', 'dev', 'pro', 'ultra'}
        for word in words:
            if len(word) > 2 and word not in common_words:
                trigger_words.append(word)
        
        # 添加中文关键词
        for word in chinese_words:
            if len(word) > 1:
                trigger_words.append(word)
        
        # 去重并限制数量
        trigger_words = list(set(trigger_words))[:10]
        return trigger_words
    
    def _is_priority_model(self, name: str, filename: str) -> bool:
        """判断是否为优先级模型"""
        text = (name + " " + filename).lower()
        for keyword in self.priority_keywords:
            if keyword.lower() in text:
                return True
        return False
    
    def create_model_config(self, model_info: Dict) -> Dict:
        """创建模型配置"""
        return {
            "name": model_info["name"],
            "filename": model_info["filename"],
            "file_path": model_info["file_path"],
            "category": model_info["category"].value,
            "weight": 0.8,
            "trigger_words": model_info["trigger_words"],
            "description": model_info.get("description", f"自动发现的Lora模型: {model_info['filename']}"),
            "civitai_id": model_info.get("civitai_id"),
            "civitai_url": model_info.get("civitai_url"),
            "rating": model_info.get("rating"),
            "downloads": model_info.get("downloads"),
            "is_local": True,
            "is_active": True,
            "is_priority": model_info["is_priority"]
        }
    
    def update_config_file(self, new_models: List[Dict], dry_run: bool = False) -> bool:
        """更新配置文件"""
        try:
            # 读取现有配置
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
            else:
                config = {"models": []}
            
            # 添加新模型
            existing_names = {model["name"] for model in config["models"]}
            added_count = 0
            
            for new_model in new_models:
                if new_model["name"] not in existing_names:
                    config["models"].append(new_model)
                    added_count += 1
            
            if dry_run:
                print(f"🔍 模拟模式: 将添加 {added_count} 个新模型")
                return True
            
            # 写回配置文件
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 成功添加 {added_count} 个新模型到配置文件")
            return True
            
        except Exception as e:
            print(f"❌ 更新配置文件失败: {e}")
            return False
    
    def discover_new_models(self, dry_run: bool = False) -> List[Dict]:
        """发现新模型"""
        print("🔍 开始自动发现LoRA模型...")
        print(f"📁 扫描目录: {self.lora_dir}")
        
        # 扫描文件
        all_files = self.scan_lora_directory()
        if not all_files:
            return []
        
        # 获取已配置的模型
        existing_files = self.get_existing_models()
        print(f"📋 已配置模型: {len(existing_files)} 个")
        
        # 找出新文件
        new_files = [f for f in all_files if f.name not in existing_files]
        print(f"🆕 发现新文件: {len(new_files)} 个")
        
        if not new_files:
            print("✅ 没有发现新的LoRA模型")
            return []
        
        # 处理新文件
        new_models = []
        for file_path in new_files:
            try:
                model_info = self.extract_model_info(file_path)
                model_config = self.create_model_config(model_info)
                new_models.append(model_config)
                
                print(f"📝 处理: {file_path.name}")
                print(f"   🏷️ 分类: {model_info['category'].value}")
                print(f"   🏷️ 触发词: {', '.join(model_info['trigger_words'][:3])}")
                print(f"   ⭐ 优先级: {'是' if model_info['is_priority'] else '否'}")
                
            except Exception as e:
                print(f"❌ 处理文件 {file_path.name} 失败: {e}")
        
        # 更新配置文件
        if new_models:
            self.update_config_file(new_models, dry_run)
        
        return new_models
    
    def show_discovery_summary(self, new_models: List[Dict]):
        """显示发现摘要"""
        if not new_models:
            return
        
        print("\n📊 发现摘要:")
        print("=" * 50)
        
        # 按分类统计
        category_stats = {}
        for model in new_models:
            category = model["category"]
            if category not in category_stats:
                category_stats[category] = []
            category_stats[category].append(model)
        
        for category, models in category_stats.items():
            print(f"🏷️ {category.upper()}: {len(models)} 个模型")
            for model in models:
                priority_mark = "⭐" if model["is_priority"] else "  "
                print(f"  {priority_mark} {model['name']}")
        
        print("\n💡 建议:")
        print("1. 检查自动生成的触发词是否合适")
        print("2. 调整模型权重和优先级设置")
        print("3. 为重要模型添加Civitai信息")


async def main():
    parser = argparse.ArgumentParser(description="LoRA模型自动发现工具")
    parser.add_argument("--dry-run", action="store_true", 
                       help="模拟模式，不实际修改配置文件")
    parser.add_argument("--force", action="store_true",
                       help="强制重新扫描所有模型")
    
    args = parser.parse_args()
    
    try:
        discoverer = LoraAutoDiscovery()
        new_models = discoverer.discover_new_models(dry_run=args.dry_run)
        discoverer.show_discovery_summary(new_models)
        
        if new_models and not args.dry_run:
            print(f"\n✅ 自动发现完成！共发现 {len(new_models)} 个新模型")
            print("🔄 建议重启LangBot以加载新配置")
        
    except KeyboardInterrupt:
        print("\n⏹️ 操作已取消")
    except Exception as e:
        print(f"❌ 自动发现失败: {e}")


if __name__ == "__main__":
    asyncio.run(main()) 