{"6": {"inputs": {"text": "cute anime girl with massive fluffy fennec ears and a big fluffy tail blonde messy long hair blue eyes wearing a maid outfit with a long black gold leaf pattern dress and a white apron mouth open placing a fancy black forest cake with candles on top of a dinner table of an old dark Victorian mansion lit by candlelight with a bright window to the foggy forest and very expensive stuff everywhere there are paintings on the walls", "clip": ["40", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Positive Prompt)"}}, "40": {"inputs": {"clip_name1": "clip_l.safetensors", "clip_name2": "t5xxl_fp8_e4m3fn.safetensors", "type": "flux", "device": "default"}, "class_type": "DualCLIPLoader", "_meta": {"title": "DualCLIPLoader"}}, "42": {"inputs": {"vae_name": "Flux_ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "50": {"inputs": {"width": 1024, "height": 1024, "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "Empty Latent Image"}}, "51": {"inputs": {"samples": ["55", 0], "vae": ["42", 0]}, "class_type": "VAEDecode", "_meta": {"title": "final_image_output"}}, "54": {"inputs": {"unet_name": "flux1_dev.safetensors", "weight_dtype": "default"}, "class_type": "UNETLoader", "_meta": {"title": "Load Diffusion Model"}}, "55": {"inputs": {"seed": "?", "sampler": "euler", "scheduler": "simple", "steps": "20", "guidance": "3.5", "max_shift": "", "base_shift": "", "denoise": "1", "model": ["62", 0], "conditioning": ["6", 0], "latent_image": ["50", 0]}, "class_type": "FluxSamplerParams+", "_meta": {"title": "🔧 Flux Sampler Parameters"}}, "62": {"inputs": {"PowerLoraLoaderHeaderWidget": {"type": "PowerLoraLoaderHeaderWidget"}, "➕ Add Lora": "", "model": ["54", 0], "clip": ["40", 0]}, "class_type": "<PERSON> Lora <PERSON> (rgthree)", "_meta": {"title": "🎨 Power LoRA Loader"}}, "63": {"inputs": {"images": ["51", 0]}, "class_type": "PreviewImage", "_meta": {"title": "Preview Image"}}}